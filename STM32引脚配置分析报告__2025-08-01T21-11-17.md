[ ] NAME:Current Task List DESCRIPTION:Root task for conversation c2ed3d23-7238-4cbf-b9da-1f00f89db507
-[ ] NAME:BNO08X引脚配置项目 DESCRIPTION:为2025template项目配置BNO08X传感器的复位引脚(PD4)和中断引脚(PD3)
--[ ] NAME:分析现有引脚配置 DESCRIPTION:检查PD3和PD4引脚在当前项目中的使用情况，确保没有冲突
--[ ] NAME:修改STM32CubeMX配置文件 DESCRIPTION:在2025template.ioc文件中添加PD3(BNO_INT)和PD4(BNO_RST)的引脚配置
--[ ] NAME:更新main.h头文件 DESCRIPTION:在main.h中添加BNO_INT_Pin、BNO_INT_GPIO_Port、BNO_RST_Pin、BNO_RST_GPIO_Port的宏定义
--[ ] NAME:修改GPIO初始化代码 DESCRIPTION:在gpio.c文件中添加PD3和PD4的GPIO初始化配置
--[ ] NAME:配置中断处理 DESCRIPTION:在stm32f4xx_it.c中添加EXTI3_IRQHandler中断处理函数
--[ ] NAME:更新NVIC中断配置 DESCRIPTION:在CubeMX配置中启用EXTI Line3中断并设置优先级
--[ ] NAME:创建BNO08X BSP层 DESCRIPTION:创建bno08x_bsp.h和bno08x_bsp.c文件，提供硬件抽象层接口
--[ ] NAME:验证配置 DESCRIPTION:检查所有修改的文件，确保配置正确且无语法错误